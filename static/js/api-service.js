/**
 * API服务类
 * 封装所有后端API调用，统一处理{code, message, data}格式响应
 */

class ApiService {
    constructor() {
        this.token = localStorage.getItem('token');
        this.baseUrl = '';
    }

    // 更新token
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('token', token);
        } else {
            localStorage.removeItem('token');
        }
    }

    // 获取认证的API工具
    getAuthApi() {
        if (!this.token) {
            throw new Error('未找到认证token');
        }
        return ApiUtils.withAuth(this.token);
    }

    // 用户认证相关API
    async login(username, password) {
        try {
            const result = await ApiUtils.post('/api/login', {
                username,
                password
            });

            if (result.success) {
                this.setToken(result.data.access_token);
                ErrorHandler.showSuccessToast('登录成功');
                return {
                    success: true,
                    user: result.data.user,
                    token: result.data.access_token
                };
            } else {
                return {
                    success: false,
                    message: result.message
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '登录失败'
            };
        }
    }

    async logout() {
        try {
            if (this.token) {
                await this.getAuthApi().post('/api/logout');
            }
        } catch (error) {
            console.error('登出API调用失败:', error);
        } finally {
            this.setToken(null);
            localStorage.removeItem('currentUser');
        }
    }

    // 对话相关API
    async getConversations() {
        try {
            const result = await this.getAuthApi().get('/api/conversations');
            if (result.success) {
                return {
                    success: true,
                    conversations: result.data || []
                };
            } else {
                return {
                    success: false,
                    message: result.message,
                    conversations: []
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '获取对话列表失败',
                conversations: []
            };
        }
    }

    async createConversation(title) {
        try {
            const result = await this.getAuthApi().post('/api/conversations/new', {
                title: title || "对话"
            });

            if (result.success) {
                ErrorHandler.showSuccessToast('创建对话成功');
                return {
                    success: true,
                    conversation: result.data
                };
            } else {
                return {
                    success: false,
                    message: result.message
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '创建对话失败'
            };
        }
    }

    async deleteConversation(conversationId) {
        try {
            const result = await this.getAuthApi().delete(`/api/conversations/${conversationId}`);
            
            if (result.success) {
                ErrorHandler.showSuccessToast('删除对话成功');
                return { success: true };
            } else {
                return {
                    success: false,
                    message: result.message
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '删除对话失败'
            };
        }
    }

    // 消息相关API
    async getMessages(conversationId) {
        try {
            const result = await this.getAuthApi().get(`/api/messages?conversation_id=${conversationId}`);
            
            if (result.success) {
                // 后端现在直接返回消息数组
                return {
                    success: true,
                    messages: result.data || []
                };
            } else {
                return {
                    success: false,
                    message: result.message,
                    messages: []
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '获取消息失败',
                messages: []
            };
        }
    }

    // 流式聊天API
    async sendStreamMessage(conversationId, message, collectionName = 'FinancialResearchOffice', inputFilters = {}, callbacks = {}) {
        const {
            onStart = () => {},
            onData = () => {},
            onError = () => {},
            onComplete = () => {},
            onReferences = () => {}  // 新增：处理引用内容的回调
        } = callbacks;

        try {
            await this.getAuthApi().stream('/api/chat/stream', {
                conversation_id: conversationId,
                message: message,
                collection_name: collectionName,
                input: inputFilters
            }, {
                onStart,
                onData,
                onError,
                onComplete,
                onReferences
            });
        } catch (error) {
            onError(error.message || '发送消息失败', error);
        }
    }

    // 获取知识库配置
    async getCollectionsConfig() {
        try {
            const result = await this.getAuthApi().get('/api/collections/config');
            if (result.code === 200) {
                return {
                    success: true,
                    data: result.data
                };
            } else {
                return {
                    success: false,
                    message: result.message,
                    data: []
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message || '获取知识库配置失败',
                data: []
            };
        }
    }

    // 检查认证状态
    isAuthenticated() {
        return !!this.token;
    }

    // 获取当前用户信息
    getCurrentUser() {
        return localStorage.getItem('currentUser');
    }
}

// 创建全局API服务实例
const apiService = new ApiService();

// 导出到全局作用域
window.ApiService = ApiService;
window.apiService = apiService;
